import CampaignForm from '@/clients/views/campaign/CampaignForm';
import { Campaign } from '@/types/campaign';
import { Nullable } from '@/types/common';

import Image from 'next/image';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

async function getCampaign<T>(slug: string) {
  console.log({ slug });
  const res = await fetch(`${BASE_URL}/admin/ad-campaigns/${slug}`, {
    next: { revalidate: 60 },
  });
  if (!res.ok) return null;
  return res.json() as T;
}

export default async function CampaignPage({ params }: { params: { slug: string | string[] } }) {
  const { slug } = params;
  const formattedSlug = (slug as string[]).join('/') ?? '';
  const campaign = await getCampaign<Nullable<Campaign>>(formattedSlug);
  if (!campaign) return <h1>Not Found</h1>;

  console.log({ campaign });

  return (
    <>
      <header className="relative w-full h-[320px] lg:h-[450px]">
        <Image
          src="/images/campaign-bg.png"
          alt="Background"
          fill
          priority
          className="hidden md:block object-cover"
        />
        <Image
          src="/images/campaign-bg-mobile.png"
          alt="Background"
          fill
          priority
          className="md:hidden object-cover"
        />
        <div className="p-5 rounded-[12px] w-[80%] bg-[rgba(43,60,87,0.8)] absolute left-1/2 -translate-x-1/2 bottom-10 text-center">
          <p className="text-[26px] font-medium tracking-[-0.52px] text-white m-0">
            {campaign.clusterHeadline}
          </p>
          <p className="text-carolina-blue text-[26px] font-medium tracking-[-0.52px] my-1">
            {campaign.regionHeadline}
          </p>
          <p className="text-white text-sm m-0">{campaign.clusterText}</p>
        </div>
      </header>
      <main>
        <CampaignForm />
      </main>
    </>
  );
}
