'use client';

import { useState, lazy, Suspense } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import { cn } from '@/lib/utils';
import { PetType } from '@/types/properties';
import InputLabel from '@/ui/atoms/InputLabel';
import { getStringSingularPlural } from '@/utils/common';

// Lazy load the heavy component only when needed
const GuestSelectorComponent = lazy(
  () => import('@/clients/views/listing-details/GuestAndPetSelector/GuestSelectorComponent'),
);

export type GuestsValues = {
  adults: number;
  children: number;
};

type Props = {
  label?: string;
  guestsValues: GuestsValues;
  setGuestsValues: (guests: GuestsValues) => void;
  petCount: number;
  setPetCount: (count: number) => void;
  petType: PetType;
  setPetType: (type: PetType) => void;
  isPetSelected: boolean;
  setIsPetSelected: (selected: boolean) => void;
  petDescription: string;
  setPetDescription: (desc: string) => void;
  petsAllowed?: boolean;
  capacity?: number;
};

const GuestAndPetSelector = ({
  label = 'GUESTS',
  guestsValues,
  setGuestsValues,
  petCount,
  setPetCount,
  isPetSelected,
  petsAllowed,
  capacity,
  petType,
  setPetType,
  petDescription,
  setPetDescription,
  setIsPetSelected,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  return (
    <>
      <InputLabel className="">{label}</InputLabel>
      <div
        className={cn(
          'border border-solid border-platinium py-2.5 px-3.5 text-sm text-metal-gray rounded-[40px] transition-all h-[42px]',
          { '!rounded-2xl !h-auto !p-3.5': open },
        )}
      >
        <div
          className="relative cursor-pointer flex items-center"
          onClick={() => setOpen(!open)}
          role="presentation"
        >
          {getStringSingularPlural('Adult', 'Adults', guestsValues.adults)},{' '}
          {getStringSingularPlural('Child', 'Children', guestsValues.children)}
          {isPetSelected && petCount > 0 && `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
          <ChevronDownIcon
            className={cn('w-4 h-4 text-foundation-blue absolute right-4', {
              'rotate-180': open,
            })}
          />
        </div>
        {open && (
          <Suspense
            fallback={<div className="p-4 text-center text-sm text-gray-500">Loading...</div>}
          >
            <GuestSelectorComponent
              petsAllowed={petsAllowed}
              capacity={capacity}
              guests={guestsValues}
              setGuests={setGuestsValues}
              petCount={petCount}
              setPetCount={setPetCount}
              petType={petType}
              setPetType={setPetType}
              petDescription={petDescription}
              setPetDescription={setPetDescription}
              isPetSelected={isPetSelected}
              setIsPetSelected={setIsPetSelected}
            />
          </Suspense>
        )}
      </div>
    </>
  );
};

export default GuestAndPetSelector;
