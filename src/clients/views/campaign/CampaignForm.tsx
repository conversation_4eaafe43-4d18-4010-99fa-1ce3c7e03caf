'use client';

import { useCallback, useMemo, useState } from 'react';

import { DateRange } from 'react-day-picker';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import { Input } from '@/components/ui/input';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { Nullable } from '@/types/common';
import { PetType } from '@/types/properties';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';

import classNames from 'classnames';

import GuestAndPetSelector from '../listing-details/GuestAndPetSelector';
import DateRangePickerInput from '../property-search/GeneralInquiryForm/DateRangePickerInput';
import DateRangePickerMobile from '../property-search/GeneralInquiryForm/DateRangePickerMobile';

type Props = {};

export type FormValues = {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  message: string;
  areas: string[];
};

const CampaignForm = () => {
  const [petCount, setPetCount] = useState<number>(1);
  const [petType, setPetType] = useState<PetType>(PetType.DOG);
  const [petDescription, setPetDescription] = useState<string>('');
  const [isPetSelected, setIsPetSelected] = useState<boolean>(false);
  const [flexibility, setFlexibility] = useState<Nullable<string>>(null);
  const [guestsValues, setGuestsValues] = useState<GuestsValues>({
    adults: 1,
    children: 0,
  });
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const { formState, errors, onChange, preSubmitCheck: preSubmitCheck } = useForm<FormValues>(
    {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      message: '',
      areas: [],
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter phone number`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
      message: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter message`;
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const dateRangePickerError = useMemo(
    () => (!date?.from || !date?.to ? 'Please select dates' : null),
    [date?.from, date?.to],
  );

  return (
    <div className="w-full md:w-[540px] md:rounded-2xl bg-white shadow p-4 mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 mb-4">
        <div>
          <InputLabel
            className={classNames('text-xs', !!errors?.firstname?.length && 'text-error')}
          >
            FIRST NAME
          </InputLabel>
          <Input
            type="text"
            name="firstname"
            value={formState.firstname}
            placeholder="Enter your first name"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.firstname ?? ''}
            error={!!errors?.firstname?.length}
          />
        </div>
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.lastname?.length && 'text-error')}>
            LAST NAME
          </InputLabel>
          <Input
            type="text"
            name="lastname"
            value={formState.lastname}
            placeholder="Enter your last name"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.lastname ?? ''}
            error={!!errors?.lastname?.length}
          />
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 mb-4">
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.email?.length && 'text-error')}>
            EMAIL
          </InputLabel>
          <Input
            type="text"
            name="email"
            value={formState.email}
            placeholder="Enter your email"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
          />
        </div>
        <div>
          <InputLabel className={classNames('text-xs', !!errors?.phone?.length && 'text-error')}>
            PHONE
          </InputLabel>
          <Input
            type="text"
            name="phone"
            value={formState.phone}
            placeholder="Enter your phone"
            className="w-full rounded-full"
            onChange={onChangeTextInput}
            helperText={errors?.email ?? ''}
            error={!!errors?.email?.length}
          />
        </div>
      </div>
      <div className="my-4">
        <div className="hidden md:block">
          <DateRangePickerInput date={date} setDate={setDate} />
        </div>
        <DateRangePickerMobile date={date} setDate={setDate} />
        {dateRangePickerError && (
          <div className="ml-2">
            <FormHelperText error>{dateRangePickerError}</FormHelperText>
          </div>
        )}
      </div>
      <GuestAndPetSelector
        guestsValues={guestsValues}
        setGuestsValues={setGuestsValues}
        petCount={petCount}
        setPetCount={setPetCount}
        isPetSelected={isPetSelected}
        petType={petType}
        setPetType={setPetType}
        petDescription={petDescription}
        setPetDescription={setPetDescription}
        setIsPetSelected={setIsPetSelected}
        petsAllowed
      />
      <div></div>
    </div>
  );
};

export default CampaignForm;
